## vue.js

**易用**

已经会了 HTML、CSS、JavaScript？即刻阅读指南开始构建应用！

**灵活**

不断繁荣的生态系统，可以在一个库和一套完整框架之间自如伸缩。

**高效**

20kB min+gzip 运行大小

超快虚拟 DOM

最省心的优化

**Vue.js 是什么**

Vue (读音 /vjuː/，类似于 view) 是一套用于构建用户界面的渐进式框架。与其它大型框架不同的是，Vue 被设计为可以自底向上逐层应用。Vue 的核心库只关注视图层，不仅易于上手，还便于与第三方库或既有项目整合。另一方面，当与现代化的工具链以及各种支持类库结合使用时，Vue 也完全能够为复杂的单页应用提供驱动。

如果你已经是有经验的前端开发者，想知道 Vue 与其它库/框架有哪些区别，请查看对比其它框架。

## Element

Element，一套为开发者、设计师和产品经理准备的基于 Vue 2.0 的桌面端组件库

**一致性** Consistency

- 与现实生活一致：与现实生活的流程、逻辑保持一致，遵循用户习惯的语言和概念；

- 在界面中一致：所有的元素和结构需保持一致，比如：设计样式、图标和文本、元素的位置等。

**反馈** Feedback

- 控制反馈：通过界面样式和交互动效让用户可以清晰的感知自己的操作；

- 页面反馈：操作后，通过页面元素的变化清晰地展现当前状态。

**效率** Efficiency

- 简化流程：设计简洁直观的操作流程；

- 清晰明确：语言表达清晰且表意明确，让用户快速理解进而作出决策；

- 帮助用户识别：界面简单直白，让用户快速识别而非回忆，减少用户记忆负担。

**可控** Controllability

- 用户决策：根据场景可给予用户操作建议或安全提示，但不能代替用户进行决策；

- 结果可控：用户可以自由的进行操作，包括撤销、回退和终止当前操作等。