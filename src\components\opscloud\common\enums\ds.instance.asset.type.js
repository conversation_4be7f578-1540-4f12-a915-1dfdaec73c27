const DsInstanceAssetType = Object.freeze({
  LDAP: {
    USER: 'USER',
    GROUP: 'GROUP'
  },
  GITLAB: {
    G<PERSON><PERSON>B_USER: 'GIT<PERSON>B_USER',
    G<PERSON><PERSON><PERSON>_SSHKEY: 'G<PERSON><PERSON>B_SSHKEY',
    GIT<PERSON><PERSON>_PROJECT: 'G<PERSON><PERSON><PERSON>_PROJECT',
    GITLAB_GROUP: 'GITLAB_GROUP'
  },
  ALIYUN: {
    ECS: 'ECS',
    VPC: 'VPC',
    V_SWITCH: 'V_SWITCH',
    ECS_IMAGE: 'ECS_IMAGE',
    ECS_SG: 'ECS_SG',
    RAM_USER: 'RAM_USER',
    RAM_POLICY: 'RAM_POLICY',
    RAM_ACCESS_KEY: 'RAM_ACCESS_KEY',
    RDS_INSTANCE: 'RDS_INSTANCE',
    RDS_DATABASE: 'RDS_DATABASE',
    POLARDB_CLUSTER: 'POLARDB_CLUSTER',
    POLARDB_DATABASE: 'POLARDB_DATABASE',
    <PERSON>LA<PERSON><PERSON><PERSON><PERSON><PERSON>_INSTANCE: 'ELASTICSEARCH_INSTANCE',
    <PERSON><PERSON><PERSON><PERSON><PERSON>_INSTANCE: 'MONGODB_INSTANCE',
    REDIS_INSTANCE: 'REDIS_INSTANCE',
    DMS_USER: 'DMS_USER',
    ONS_ROCKETMQ_INSTANCE: 'ONS_ROCKETMQ_INSTANCE',
    ONS_ROCKETMQ_TOPIC: 'ONS_ROCKETMQ_TOPIC',
    ONS_ROCKETMQ_GROUP: 'ONS_ROCKETMQ_GROUP',
    ALIYUN_DOMAIN: 'ALIYUN_DOMAIN',
    ACR_INSTANCE: 'ACR_INSTANCE',
    ACR_NAMESPACE: 'ACR_NAMESPACE',
    ACR_REPOSITORY: 'ACR_REPOSITORY'
  },
  ALIYUN_DEVOPS: {
    ALIYUN_DEVOPS_PROJECT: 'ALIYUN_DEVOPS_PROJECT',
    ALIYUN_DEVOPS_SPRINT: 'ALIYUN_DEVOPS_SPRINT',
    ALIYUN_DEVOPS_WORKITEM: 'ALIYUN_DEVOPS_WORKITEM'
  },
  AWS: {
    EC2: 'EC2',
    IAM_POLICY: 'IAM_POLICY',
    IAM_USER: 'IAM_USER',
    IAM_ACCESS_KEY: 'IAM_ACCESS_KEY',
    SQS: 'SQS',
    SNS_TOPIC: 'SNS_TOPIC',
    SNS_SUBSCRIPTION: 'SNS_SUBSCRIPTION',
    AMAZON_DOMAIN: 'AMAZON_DOMAIN',
    ECR_REPOSITORY: 'ECR_REPOSITORY'
  },
  KUBERNETES: {
    KUBERNETES_NODE: 'KUBERNETES_NODE',
    KUBERNETES_NAMESPACE: 'KUBERNETES_NAMESPACE',
    KUBERNETES_DEPLOYMENT: 'KUBERNETES_DEPLOYMENT',
    KUBERNETES_SERVICE: 'KUBERNETES_SERVICE',
    KUBERNETES_POD: 'KUBERNETES_POD',
    KUBERNETES_INGRESS: 'KUBERNETES_INGRESS'
  },
  ANSIBLE: {
    ANSIBLE_VERSION: 'ANSIBLE_VERSION',
    ANSIBLE_HOSTS: 'ANSIBLE_HOSTS'
  },
  ZABBIX: {
    ZABBIX_USER: 'ZABBIX_USER',
    ZABBIX_USER_GROUP: 'ZABBIX_USER_GROUP',
    ZABBIX_HOST: 'ZABBIX_HOST',
    ZABBIX_HOST_GROUP: 'ZABBIX_HOST_GROUP',
    ZABBIX_TEMPLATE: 'ZABBIX_TEMPLATE',
    ZABBIX_TRIGGER: 'ZABBIX_TRIGGER'
  },
  JENKINS: {
    JENKINS_COMPUTER: 'JENKINS_COMPUTER',
    JENKINS_TEMPLATE: 'JENKINS_TEMPLATE'
  },
  GUACAMOLE: {
    GUACAMOLE: 'GUACAMOLE'
  },
  TENCENT_EXMAIL: {
    TENCENT_EXMAIL_USER: 'TENCENT_EXMAIL_USER'
  },
  NEXUS: {
    NEXUS_ASSET: 'NEXUS_ASSET'
  },
  SONAR: {
    SONAR_PROJECT: 'SONAR_PROJECT'
  },
  NACOS: {
    NACOS_CLUSTER_NODE: 'NACOS_CLUSTER_NODE',
    NACOS_PERMISSION: 'NACOS_PERMISSION',
    NACOS_USER: 'NACOS_USER',
    NACOS_ROLE: 'NACOS_ROLE'
  },
  DINGTALK_APP: {
    DINGTALK_USER: 'DINGTALK_USER',
    DINGTALK_DEPARTMENT: 'DINGTALK_DEPARTMENT'
  },
  HUAWEICLOUD: {
    HUAWEICLOUD_ECS: 'HUAWEICLOUD_ECS'
  },
  CONSUL: {
    CONSUL_SERVICE: 'CONSUL_SERVICE'
  },
  METER_SPHERE: {
    METER_SPHERE_BUILD_HOOK: 'METER_SPHERE_BUILD_HOOK',
    METER_SPHERE_DEPLOY_HOOK: 'METER_SPHERE_DEPLOY_HOOK'
  },
  APOLLO: {
    APOLLO_APP: 'APOLLO_APP',
    APOLLO_INTERCEPT_RELEASE: 'APOLLO_INTERCEPT_RELEASE'
  },
  ALIYUN_ARMS: {
    ALIYUN_ARMS_TRACE_APP: 'ALIYUN_ARMS_TRACE_APP'
  }
})

export default DsInstanceAssetType
