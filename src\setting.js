export default {
  // 快捷键
  // 支持快捷键 例如 ctrl+shift+s
  hotkey: {
    search: {
      open: 's',
      close: 'esc'
    }
  },
  // 侧边栏默认配置
  menu: {
    asideCollapse: false,
    asideTransition: true
  },
  // 在读取持久化数据失败时默认页面
  page: {
    opened: [
      {
        name: 'index',
        fullPath: '/index',
        meta: {
          title: '首页',
          auth: false
        }
      }
    ]
  },
  // 菜单搜索
  search: {
    enable: true
  },
  // 注册的主题
  theme: {
    list: [
      {
        title: 'd2admin 经典',
        name: 'd2',
        preview: 'image/theme/d2/<EMAIL>'
      },
      {
        title: '<PERSON>',
        name: 'chester',
        preview: 'image/theme/chester/<EMAIL>'
      },
      {
        title: 'Element',
        name: 'element',
        preview: 'image/theme/element/<EMAIL>'
      },
      {
        title: '紫罗兰',
        name: 'violet',
        preview: 'image/theme/violet/<EMAIL>'
      },
      {
        title: '简约线条',
        name: 'line',
        backgroundImage: 'image/theme/line/bg.jpg',
        preview: 'image/theme/line/<EMAIL>'
      },
      {
        title: '流星',
        name: 'star',
        backgroundImage: 'image/theme/star/bg.jpg',
        preview: 'image/theme/star/<EMAIL>'
      },
      {
        title: 'Tomorrow Night Blue (vsCode)',
        name: 'tomorrow-night-blue',
        preview: 'image/theme/tomorrow-night-blue/<EMAIL>'
      }
    ]
  },
  // 是否默认开启页面切换动画
  transition: {
    active: true
  }
}
