![banner](https://raw.githubusercontent.com/FairyEver/d2-admin/master/docs/image/banner.png)

<p align="center">
	<a href="https://github.com/d2-projects/d2-admin/stargazers" target="_blank"><img src="https://img.shields.io/github/stars/d2-projects/d2-admin.svg"></a>
	<a href="https://github.com/d2-projects/d2-admin/network/members" target="_blank"><img src="https://img.shields.io/github/forks/d2-projects/d2-admin.svg"></a>
	<a href="https://github.com/d2-projects/d2-admin/issues" target="_blank"><img src="https://img.shields.io/github/issues/d2-projects/d2-admin.svg"></a>
	<a href="https://github.com/d2-projects/d2-admin/issues?q=is%3Aissue+is%3Aclosed" target="_blank"><img src="https://img.shields.io/github/issues-closed/d2-projects/d2-admin.svg"></a>
	<a href="https://github.com/d2-projects/d2-admin/pulls" target="_blank"><img src="https://img.shields.io/github/issues-pr/d2-projects/d2-admin.svg"></a>
	<a href="https://github.com/d2-projects/d2-admin/pulls?q=is%3Apr+is%3Aclosed" target="_blank"><img src="https://img.shields.io/github/issues-pr-closed/d2-projects/d2-admin.svg"></a>
	<a href="https://github.com/d2-projects/d2-admin" target="_blank"><img src="https://img.shields.io/github/last-commit/d2-projects/d2-admin.svg"></a>
</p>
<p align="center">
	<a href="https://github.com/d2-projects/d2-admin" target="_blank"><img src="https://visitor-badge.glitch.me/badge?page_id=d2-projects.d2-admin"></a>
	<a href="https://github.com/d2-projects/d2-admin/releases" target="_blank"><img src="https://img.shields.io/github/release/d2-projects/d2-admin.svg"></a>
	<a href="https://deepscan.io/dashboard#view=project&tid=8014&pid=10161&bid=136697"><img src="https://deepscan.io/api/teams/8014/projects/10161/branches/136697/badge/grade.svg" alt="DeepScan grade"></a>
</p>

[D2Admin](https://github.com/d2-projects/d2-admin) is a fully open source and free enterprise back-end product front-end integration solution, using the latest front-end technology stack, javascript files loading of local first screen less than 60kb, has prepared most of the project preparations, and with a lot of sample code to help the management system agile development.

[中文](https://github.com/d2-projects/d2-admin/blob/master/README.zh.md) | **English**

## Preview

![Deploy preview](https://github.com/d2-projects/d2-admin/workflows/Deploy%20preview/badge.svg)
[![Netlify Status](https://api.netlify.com/api/v1/badges/a5dd4bbd-da3f-4145-98a9-8012577bdcf5/deploy-status)](https://app.netlify.com/sites/d2-admin/deploys)

The following access addresses are built and deployed by the latest master branch code at the same time. The access effect is completely consistent. Please select the appropriate access link according to your own network situation.

| server | link | server |
| --- | --- | --- |
| d2.pub | [Link](https://d2.pub/d2-admin/preview) | China server |
| cdn.d2.pub | [Link](https://cdn.d2.pub/d2-admin/preview) | qiniu CDN |
| github | [Link](https://d2-projects.github.io/d2-admin) | GitHub pages |
| netlify | [Link](https://d2-admin.netlify.com) | Netlify CDN |

## Document

[document on https://d2.pub](https://d2.pub/zh/doc/d2-admin/)

> d2.pub CDN mirror deployment [https://cdn.d2.pub](https://cdn.d2.pub)

## Features

* Build with vue-cli3
* First screen loading waiting animation
* Five themes
* Built-in UEditor rich text editor
* Detailed documentation
* Login and logout
* Separate routing and menu settings
* Foldable sidebar
* Multi-national language
* Rich text editor
* Markdown editor
* full screen
* Fontawesome icon library
* Icon selector
* Automatically register SVG icon
* Simulation data
* Clipboard package
* Chart library
* Time and date calculation tool
* Import Excel ( xlsx + csv )
* Data export Excel ( xlsx + csv )
* Data export text
* Digital animation
* Drag and drop the size of the block layout
* Grid layout for drag and resize and position
* Out-of-the-box page layout components
* Load and parse markdown files
* GitHub style markdown display component
* markdown internal code highlighting
* Expanded Baidu cloud link resolution and optimized display for markdown
* Right click menu component
* Custom scrollbars and scrolling controls
* Common style extraction, convenient theme customization
* Support temporary menu configuration
* System function display module `1.1.4 +`
* Multi-tab mode `1.1.4 +`
* Beautify the scroll bar `1.1.4 +`
* json view `1.1.4 +`
* cookie wrapper `1.1.5 +`
* Multi-tab global control API `1.1.5 +`
* Menu Global Control API `1.1.5 +`
* Multi-tab page close control support right-click menu `1.1.10 +`
* Modular global state management `1.2.0 +`
* Multiple data persistence methods: distinguish users, distinguish routes, page data snapshot function `1.2.0 +`
* Support for menu system that jumps out of external links `1.2.0 +`
* Support menu svg icon `1.3.0 +`
* Logging and error catching `1.3.0 +`
* Global menu search `1.3.0 +`
* Custom login redirect `1.3.0 +`
* Switch global base component size `1.4.0 +`
* Page loading progress bar `1.4.1 +`
* Adaptive top menu bar `1.4.7 +`
* Support for merging cells when exporting xslx `1.5.4 +`
* Multiple tabs support drag and drop sorting `1.8.0 +`
* load only local JavaScript code less than 60kb on the homepage `1.8.0 +`
* Built in build file volume checking tool `1.8.0 +`
* Example of multi page `1.23.0 +`
* Split chunks `1.23.0 +`

## Other synchronous repositories

| type | link |
| --- | --- |
| gitee | [https://gitee.com/d2-projects/d2-admin](https://gitee.com/d2-projects/d2-admin) |
| coding | [https://d2-projects.coding.net/p/d2-projects/d/d2-admin/git](https://d2-projects.coding.net/p/d2-projects/d/d2-admin/git) |

## Other versions

| Name | HomePage  | Preview | Introduction |
| --- | --- | --- | --- |
| Starter template | [Link](https://github.com/d2-projects/d2-admin-start-kit) | [Link](https://d2.pub/d2-admin-start-kit/preview) | The simplest version |

## Community projects

> These projects are contributed by the open source community and are not guaranteed to use the latest version of D2Admin. Please contact their open source authors for related usage questions.

| Name | HomePage | Preview | Introduction |
| --- | --- | --- | --- |
| d2-admin-xiya-go-cms | [Link](https://github.com/d2-projects/d2-admin-xiya-go-cms) | [Link](https://d2.pub/d2-admin-xiya-go-cms/preview) | D2Admin + authority system + dynamic router |
| d2-advance | [Link](https://github.com/d2-projects/d2-advance) | [Link](https://d2.pub/d2-advance/preview) | Technical exploration inspired by D2Admin |
| d2-crud-plus | [Link](https://github.com/greper/d2-crud-plus) | [Link](http://qiniu.veryreader.com/D2CrudPlusExample/index.html) | Easy development of crud function |
| d2-crud | [Link](https://github.com/d2-projects/d2-crud) | [Link]() | Encapsulation of common operations in tables |
| d2-admin-pm | [Link](https://github.com/wjkang/d2-admin-pm) | [Link](http://jaycewu.coding.me/d2-admin-pm) | RBAC privilege management solution based on D2Admin |
| LanBlog | [Link](https://github.com/sinksmell/LanBlog) | [Link](http://47.101.222.133/) | Vue + Beego restful api personal blog system |
| d2-admin-start-kit-plus | [Link](https://github.com/hank-cp/d2-admin-start-kit-plus) | [Link](https://github.com/hank-cp/d2-admin-start-kit-plus) | D2Admin Start kit modular version |
| d2-ribbons | [Link](https://github.com/d2-projects/d2-ribbons) | [Link](https://github.com/d2-projects/d2-ribbons) | Open source project logo Library |

## Open source backend implementation

> The backend is contributed by the open source community. The latest version of D2Admin is not guaranteed. Please contact its open source author for related usage issues.

| Name | technology | HomePage | Preview | Introduction |
| --- | --- | --- | --- | --- |
| boot-admin | SpringBoot | [Link](https://github.com/hb0730/boot-admin) | [Link](http://admin.hb0730.com/) | Management system based on SpringBoot |
| FlaskPermission | Flask | [Link](https://github.com/huguodong/flask-permission) | [Link](http://47.97.218.139:9999) | Permission management based on Flask |
| CareyShop | ThinkPHP5 | [Link](https://github.com/dnyz520/careyshop-admin) | [Link](https://demo.careyshop.cn/admin/) | High Performance Mall Framework System for CareyShop |
| jiiiiiin-security | Spring Boot | [Link](https://github.com/Jiiiiiin/jiiiiiin-security) | [Link](https://github.com/Jiiiiiin/jiiiiiin-security) | Content management infrastructure projects |
| Taroco | Spring Cloud | [Link](https://github.com/liuht777/Taroco) | [Link](http://***************/) | Complete microservice enterprise solution |
| Aooms | Spring Cloud | [Link](https://gitee.com/cyb-javaer/Aooms) | [Link](https://www.yuboon.com/Aooms) | Extremely fast microservice development, not just as simple as JFinal |
| GOA | Beego | [Link](https://github.com/Qsnh/goa) | [Link](http://goaio.vip/) | Online question answering system based on Beego + Vue |
| CMDB | Django | [Link](https://github.com/CJFJack/django_vue_cmdb) | [Link](https://mp.weixin.qq.com/s?__biz=MzU1OTYzODA4Mw==&mid=2247484250&idx=1&sn=981024ac0580d8a3eba95742bd32b268) | authority system with dynamic menu |

## Badge

If your open source project is based on D2Admin development, please add the following badge to your README:

<a href="https://github.com/d2-projects/d2-admin" target="_blank">
	<img src="https://raw.githubusercontent.com/FairyEver/d2-admin/master/docs/image/<EMAIL>" width="200">
</a>

Copy the following code into the README to:

``` html
<a href="https://github.com/d2-projects/d2-admin" target="_blank"><img src="https://raw.githubusercontent.com/FairyEver/d2-admin/master/docs/image/<EMAIL>" width="200"></a>
```

At the same time, you can report your project to us. We will place the excellent project in D2Admin and help you publicize it.

## Contributor

* [@FairyEver](https://github.com/FairyEver)
* [@sunhaoxiang](https://github.com/sunhaoxiang)
* [@Aysnine](https://github.com/Aysnine)
* [@luchaohai](https://github.com/luchaohai)
* [@han-feng](https://github.com/han-feng)
* [@rongxingsun](https://github.com/rongxingsun)
* [@dnyz520](https://github.com/dnyz520)

## Become a sponsor

[Sponsor me on afdian.net](https://afdian.net/@fairyever)

## Sponsor

**cochlea** | **Baron** | **苦行僧** | **吴地安宁** | **KingDong** | **sunyongmofang**

## Visitor

![Total visitor](https://visitor-badge.glitch.me/badge?page_id=d2-projects.d2-admin)

> Total visitor since 2019.08.27

## Star history

[![Stargazers over time](https://starchart.cc/d2-projects/d2-admin.svg)](https://starchart.cc/d2-projects/d2-admin)

## License

[![FOSSA Status](https://app.fossa.com/api/projects/git%2Bgithub.com%2Fd2-projects%2Fd2-admin.svg?type=large)](https://app.fossa.com/projects/git%2Bgithub.com%2Fd2-projects%2Fd2-admin?ref=badge_large)
