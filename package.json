{"name": "d2-admin", "version": "1.24.0", "scripts": {"serve": "vue-cli-service serve --open --host 127.0.0.1 --port 8000", "start": "npm run serve", "dev": "npm run serve", "build": "vue-cli-service build --report", "build:preview": "vue-cli-service build --mode preview", "build:prod": "vue-cli-service build --mode prod", "lint": "vue-cli-service lint --fix", "test:unit": "vue-cli-service test:unit"}, "dependencies": {"@d2-projects/vue-table-export": "^1.1.2", "@d2-projects/vue-table-import": "^1.0.1", "@fortawesome/fontawesome-free": "^5.15.3", "animate.css": "^4.1.1", "axios": "^0.19.0", "axios-mock-adapter": "^1.18.1", "better-scroll": "^1.15.2", "clipboard-polyfill": "^2.8.6", "core-js": "^3.4.3", "countup.js": "^2.0.4", "dayjs": "^1.8.17", "echarts": "^5.4.1", "echarts-wordcloud": "^2.1.0", "element-ui": "^2.13.2", "faker": "^4.1.0", "flex.css": "^1.1.7", "fuse.js": "^5.2.3", "github-markdown-css": "^4.0.0", "guacamole-common-js": "^1.3.0", "highlight.js": "^10.0.2", "hotkeys-js": "^3.7.3", "jenkins-pipeline-graph-vue": "^1.0.8", "js-cookie": "^2.2.1", "leader-line-vue": "^2.1.1", "lodash": "^4.17.20", "lowdb": "^1.0.0", "markdown-it-vue": "^1.1.6", "marked": "^1.0.0", "nprogress": "^0.2.0", "qs": "^6.9.4", "quill": "^1.3.7", "screenfull": "^5.0.2", "simplemde": "^1.11.2", "sortablejs": "^1.10.1", "ua-parser-js": "^0.7.20", "vant": "^2.11.1", "vue": "^2.6.11", "vue-clipboard2": "^0.3.3", "vue-echarts": "^6.5.4", "vue-grid-layout": "^2.3.7", "vue-i18n": "^8.15.1", "vue-json-tree-view": "^2.1.4", "vue-qr": "^3.2.4", "vue-router": "^3.1.3", "vue-splitpane": "^1.0.6", "vue-ueditor-wrap": "^2.4.1", "vue2-ace-editor": "0.0.15", "vuedraggable": "^2.24.3", "vuex": "^3.1.2", "xterm": "^4.12.0", "xterm-addon-fit": "^0.5.0"}, "devDependencies": {"@d2-projects/vue-filename-injector": "^1.1.0", "@kazupon/vue-i18n-loader": "^0.5.0", "@vue/cli-plugin-babel": "^4.1.0", "@vue/cli-plugin-eslint": "^4.1.0", "@vue/cli-plugin-router": "^4.1.0", "@vue/cli-plugin-unit-jest": "^4.1.0", "@vue/cli-plugin-vuex": "^4.1.0", "@vue/cli-service": "^4.1.0", "@vue/composition-api": "^1.7.1", "@vue/eslint-config-standard": "^5.1.2", "@vue/test-utils": "^1.0.2", "babel-eslint": "^10.0.3", "babel-plugin-import": "^1.13.3", "compression-webpack-plugin": "^3.0.1", "cz-conventional-changelog": "^3.2.0", "eslint": "^6.8.0", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-vue": "^6.2.2", "less": "^3.12.2", "less-loader": "^7.1.0", "sass": "^1.23.7", "sass-loader": "^8.0.0", "svg-sprite-loader": "^4.1.6", "text-loader": "^0.0.1", "vue-cli-plugin-i18n": "^1.0.1", "vue-template-compiler": "^2.6.10", "webpack-bundle-analyzer": "^3.6.0", "webpack-theme-color-replacer": "^1.3.3"}, "config": {"commitizen": {"path": "./node_modules/cz-conventional-changelog"}}, "repository": {"type": "git", "url": "https://github.com/d2-projects/d2-admin.git"}}