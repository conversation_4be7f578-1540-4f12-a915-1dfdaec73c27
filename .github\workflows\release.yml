name: Release pipeline

on:
  push:
    branches:
      - master

jobs:
  publish:
    if: "! contains(github.event.head_commit.message, '[skip ci]')"
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v1
    - name: Semantic Release
      uses: cycjimmy/semantic-release-action@v2
      with:
        extra_plugins: |
          @semantic-release/changelog@3.0.6
          @semantic-release/git@7.0.18
      env:
        GITHUB_TOKEN: ${{ secrets.ACCESS_TOKEN }}

  # !TRICKY
  # https://github.community/t5/GitHub-Actions/Workflow-is-failing-if-no-job-can-be-ran-due-to-condition/td-p/38085
  always_job:
    name: Always run job
    runs-on: ubuntu-latest
    steps:
      - name: Always run
        run: echo "TRICKY run"