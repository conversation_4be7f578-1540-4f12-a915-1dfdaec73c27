const WorkOrderKeyConstants = Object.freeze({
  RAM_POLICY: 'RAM_POLICY',
  IAM_POLICY: 'IAM_POLICY',
  AWS_IAM_UPDATE_LOGIN_PROFILE: 'AWS_IAM_UPDATE_LOGIN_PROFILE',
  ALIYUN_RAM_UPDATE_LOGIN_PROFILE: 'ALIYUN_RAM_UPDATE_LOGIN_PROFILE',
  ONS_ROCKETMQ_TOPIC: 'ONS_ROCKETMQ_TOPIC',
  ONS_ROCKETMQ_GROUP: 'ONS_ROCKETMQ_GROUP',
  SERVER_GROUP: 'SERVER_GROUP',
  APPLICATION_PERMISSION: 'APPLICATION_PERMISSION',
  VPN: 'VPN',
  GRAFANA: 'GRAFANA',
  NEXUS: 'NEXUS',
  CONFLUENCE: 'CONFLUENCE',
  NACOS: 'NACOS',
  GITLAB_PROJECT: 'GITLAB_PROJECT',
  GITLAB_GROUP: 'GITLAB_GROUP',
  SYS_EMPLOYEE_RESIGN: 'SYS_EMPLOYEE_RESIGN',
  SQS: 'SQS',
  SNS_TOPIC: 'SNS_TOPIC',
  SNS_SUBSCRIPTION: 'SNS_SUBSCRIPTION',
  APPLICATION_SCALE_REPLICAS: 'APPLICATION_SCALE_REPLICAS',
  APPLICATION_REDUCE_REPLICAS: 'APPLICATION_REDUCE_REPLICAS',
  APOLLO: 'APOLLO',
  APPLICATION_DEPLOY: 'APPLICATION_DEPLOY',
  NEW_APPLICATION: 'NEW_APPLICATION',
  APOLLO_RELEASE: 'APOLLO_RELEASE',
  SER_DEPLOY: 'SER_DEPLOY'
})

export default WorkOrderKeyConstants
