const LeoRequestType = Object.freeze({
  SUBSCRIBE_LEO_JOB: 'SUBSCRIBE_LEO_JOB',
  SUBSCRIBE_LEO_BUILD: 'SUBSCRIBE_LEO_BUILD',
  QUERY_LEO_BUILD_CONSOLE_STREAM: 'QUERY_LEO_BUILD_CONSOLE_STREAM',
  SUBSCRIBE_LEO_DEPLOY: 'SUBSCRIBE_LEO_DEPLOY',
  SUBSCRIBE_LEO_DEPLOY_DETAILS: 'SUBSCRIBE_LEO_DEPLOY_DETAILS',
  SUBSCRIBE_LEO_DEPLOYMENT_VERSION_DETAILS: 'SUBSCRIBE_LEO_DEPLOYMENT_VERSION_DETAILS',
  AUTHENTICATION_FAILURE: 'AUTHENTICATION_FAILURE'
})

export default LeoRequestType
