// 每个主题特有的设置
.theme-#{$theme-name} {

  .el-message {
    &.el-message--info {
      background-color: $theme-message-info-background-color;
      color: $theme-message-info-text-color;
      border-color: $theme-message-info-border-color;
    }
  }

  .el-card {
    &.d2-card {
      border: $theme-container-border-outer;
      .el-card__header {
        border-bottom: $theme-container-border-outer;
      }
    }
  }

  // 背景图片和遮罩
  .d2-layout-header-aside-group {
    background-color: $theme-bg-color;
    .d2-layout-header-aside-mask {
      background: $theme-bg-mask;
    }
  }

  // 菜单项目
  @mixin theme-menu-hover-style {
    color: $theme-menu-item-color-hover;
    i.fa {
      color: $theme-menu-item-color-hover;
    }
    background: $theme-menu-item-background-color-hover;
  }
  %el-menu-icon {
    i {
      display: inline-block;
      width: 14px;
      text-align: center;
      margin-right: 5px;
    }
    svg {
      margin: 0px;
      height: 14px;
      width: 14px;
      margin-right: 5px;
    }
  }
  .el-submenu__title {
    @extend %unable-select;
    @extend %el-menu-icon;
  }
  .el-menu-item {
    @extend %unable-select;
    @extend %el-menu-icon;
  }
  .el-submenu__title:hover {
    @include theme-menu-hover-style;
  }
  .el-menu-item:hover {
    @include theme-menu-hover-style;
  }
  .el-menu--horizontal .el-menu-item:not(.is-disabled):hover {
    @include theme-menu-hover-style;
  }
  .el-menu--horizontal .el-menu .el-submenu__title:hover {
    @include theme-menu-hover-style;
  }
  
  // 顶栏
  .d2-theme-header {
    // 顶栏菜单空间不足时显示的滚动控件
    .d2-theme-header-menu {
      .d2-theme-header-menu__prev, .d2-theme-header-menu__next {
        color: $theme-header-item-color;
        background: $theme-header-item-background-color;
        &:hover {
          color: $theme-header-item-color-hover;
          background: $theme-header-item-background-color-hover;
        }
      }
    }
    // 切换按钮
    .toggle-aside-btn {
      i {
        color: $theme-header-item-color;
        background: $theme-header-item-background-color;
        &:hover {
          color: $theme-header-item-color-hover;
        }
      }
    }
    // 顶栏菜单
    .el-menu {
      .el-menu-item {
        transition: border-top-color 0s;
        color: $theme-header-item-color;
        background: $theme-header-item-background-color;
        i.fa { color: inherit; }
        &:hover {
          color: $theme-header-item-color-hover;
          background: $theme-header-item-background-color-hover;
          i.fa { color: inherit; }
        }
        &:focus {
          color: $theme-header-item-color-focus;
          background: $theme-header-item-background-color-focus;
          i.fa { color: inherit; }
        }
        &.is-active {
          color: $theme-header-item-color-active;
          background: $theme-header-item-background-color-active;
          i.fa { color: inherit; }
        }
      }
      .el-submenu {
        &.is-active {
          .el-submenu__title {
            color: $theme-header-item-color-active;
            background: $theme-header-item-background-color-active;
            i.fa { color: inherit; }
          }
        }
        .el-submenu__title {
          transition: border-top-color 0s;
          color: $theme-header-item-color;
          background: $theme-header-item-background-color;
          i.fa { color: inherit; }
          .el-submenu__icon-arrow {
            color: $theme-header-item-color;
          }
          &:hover {
            color: $theme-header-item-color-hover;
            background: $theme-header-item-background-color-hover;
            i.fa { color: inherit; }
            .el-submenu__icon-arrow {
              color: $theme-header-item-color-hover;
            }
          }
          &:focus {
            color: $theme-header-item-color-focus;
            background: $theme-header-item-background-color-focus;
            i.fa { color: inherit; }
            .el-submenu__icon-arrow {
              color: $theme-header-item-color-focus;
            }
          }
        }
      }
    }
    // 顶栏右侧
    .d2-header-right {
      .btn-text {
        color: $theme-header-item-color;
        &.can-hover {
          &:hover {
            color: $theme-header-item-color-hover;
            background: $theme-header-item-background-color-hover;
          }
        }
      }
    }
  }
  // [布局] 顶栏下面
  .d2-theme-container {
    // 侧边栏
    .d2-theme-container-aside {
      // 菜单为空的时候显示的信息
      .d2-layout-header-aside-menu-empty {
        background: $theme-aside-menu-empty-background-color;
        i {
          color: $theme-aside-menu-empty-icon-color;
        }
        span {
          color: $theme-aside-menu-empty-text-color;
        }
        &:hover {
          background: $theme-aside-menu-empty-background-color-hover;
          i {
            color: $theme-aside-menu-empty-icon-color-hover;
          }
          span {
            color: $theme-aside-menu-empty-text-color-hover;
          }
        }
      }
      // [菜单] 正常状态
      .el-menu {
        .el-menu-item {
          color: $theme-aside-item-color;
          background: $theme-aside-item-background-color;
          i {
            color: $theme-aside-item-color;
          }
          &:hover {
            color: $theme-aside-item-color-hover;
            fill: $theme-aside-item-color-hover;
            background: $theme-aside-item-background-color-hover;
            i {
              color: $theme-aside-item-color-hover;
            }
          }
          &:focus {
            color: $theme-aside-item-color-focus;
            fill: $theme-aside-item-color-focus;
            background: $theme-aside-item-background-color-focus;
            i {
              color: $theme-aside-item-color-focus;
            }
          }
          &.is-active {
            color: $theme-aside-item-color-active;
            fill: $theme-aside-item-color-active;
            background: $theme-aside-item-background-color-active;
            i {
              color: $theme-aside-item-color-active;
            }
          }
        }
      }
      .el-submenu {
        .el-submenu__title {
          color: $theme-aside-item-color;
          background: $theme-aside-item-background-color;
          i {
            color: $theme-aside-item-color;
          }
          .el-submenu__icon-arrow {
            color: $theme-aside-item-color;
          }
          &:hover {
            color: $theme-aside-item-color-hover;
            background: $theme-aside-item-background-color-hover;
            i {
              color: $theme-aside-item-color-hover;
            }
            .el-submenu__icon-arrow {
              color: $theme-aside-item-color-hover;
            }
          }
        }
      }
    }
    .d2-theme-container-main {
      // 主体部分分为多页面控制器 和主体
      .d2-theme-container-main-header {
        // 多页面控制器
        .d2-multiple-page-control {
          .el-tabs__header.is-top {
            border-bottom-color: $theme-multiple-page-control-border-color;
          }
          .el-tabs__nav {
            border-color: $theme-multiple-page-control-border-color;
            .el-tabs__item {
              @extend %unable-select;
              color: $theme-multiple-page-control-color;
              background-color: $theme-multiple-page-control-background-color;
              border-left-color: $theme-multiple-page-control-border-color;
              &:first-child {
                border-left: none;
                &:hover {
                  padding: 0px 20px;
                }
              }
            }
            .el-tabs__item.is-active {
              color: $theme-multiple-page-control-color-active;
              background-color: $theme-multiple-page-control-background-color-active;
              border-bottom-color: $theme-multiple-page-control-border-color-active;
            }
          }
          %el-tabs__nav {
            font-size: 20px;
          }
          .el-tabs__nav-prev {
            @extend %el-tabs__nav;
            color: $theme-multiple-page-control-nav-prev-color;
          }
          .el-tabs__nav-next {
            @extend %el-tabs__nav;
            color: $theme-multiple-page-control-nav-next-color;
          }
        }
        // 多页控制器的关闭控制
        .d2-multiple-page-control-btn {
          .el-dropdown {
            .el-button-group {
              .el-button {
                border-color: $theme-multiple-page-control-border-color;
              }
            }
          }
        }
      }
      // 主体
      .d2-theme-container-main-body {
        // 布局组件
        .container-component {
          // [组件]
          // d2-container-full 填充型
          .d2-container-full {
            border: $theme-container-border-outer;
            border-top: none;
            border-bottom: none;
            .d2-container-full__header {
              border-bottom: $theme-container-border-inner;
              background: $theme-container-header-footer-background-color;
            }
            .d2-container-full__body {
              background: $theme-container-background-color;
            }
            .d2-container-full__footer {
              border-top: $theme-container-border-inner;
              background: $theme-container-header-footer-background-color;
            }
          }
          // [组件]
          // d2-container-full-bs 填充型 滚动优化
          .d2-container-full-bs {
            border: $theme-container-border-outer;
            border-top: none;
            border-bottom: none;
            .d2-container-full-bs__header {
              border-bottom: $theme-container-border-inner;
              background: $theme-container-header-footer-background-color;
            }
            .d2-container-full-bs__body {
              background: $theme-container-background-color;
            }
            .d2-container-full-bs__footer {
              border-top: $theme-container-border-inner;
              background: $theme-container-header-footer-background-color;
            }
          }
          // [组件]
          // d2-container-ghost 隐形布局组件
          .d2-container-ghost {
            .d2-container-ghost__header {
              border-bottom: $theme-container-border-outer;
              border-left: $theme-container-border-outer;
              border-right: $theme-container-border-outer;
              background: $theme-container-header-footer-background-color;
            }
            .d2-container-ghost__footer {
              border-top: $theme-container-border-outer;
              border-left: $theme-container-border-outer;
              border-right: $theme-container-border-outer;
              background: $theme-container-header-footer-background-color;
            }
          }
          // [组件]
          // d2-container-ghost-bs 隐形布局组件 滚动优化
          .d2-container-ghost-bs {
            .d2-container-ghost-bs__header {
              border-bottom: $theme-container-border-outer;
              border-left: $theme-container-border-outer;
              border-right: $theme-container-border-outer;
              background: $theme-container-header-footer-background-color;
            }
            .d2-container-ghost-bs__footer {
              border-top: $theme-container-border-outer;
              border-left: $theme-container-border-outer;
              border-right: $theme-container-border-outer;
              background: $theme-container-header-footer-background-color;
            }
          }
          // [组件]
          // d2-container-card 卡片型
          .d2-container-card {
            .d2-container-card__header {
              border-bottom: $theme-container-border-inner;
              border-left: $theme-container-border-outer;
              border-right: $theme-container-border-outer;
              background: $theme-container-header-footer-background-color;
            }
            .d2-container-card__body {
              .d2-container-card__body-card {
                background: $theme-container-background-color;
                border-left: $theme-container-border-outer;
                border-right: $theme-container-border-outer;
                border-bottom: $theme-container-border-outer;
              }
            }
            .d2-container-card__footer {
              border-top: $theme-container-border-outer;
              border-left: $theme-container-border-outer;
              border-right: $theme-container-border-outer;
              background: $theme-container-header-footer-background-color;
            }
          }
          // [组件]
          // d2-container-card-bs 卡片型 滚动优化
          .d2-container-card-bs {
            .d2-container-card-bs__header {
              border-bottom: $theme-container-border-inner;
              border-left: $theme-container-border-outer;
              border-right: $theme-container-border-outer;
              background: $theme-container-header-footer-background-color;
            }
            .d2-container-card-bs__body {
              .d2-container-card-bs__body-card {
                background: $theme-container-background-color;
                border-left: $theme-container-border-outer;
                border-right: $theme-container-border-outer;
                border-bottom: $theme-container-border-outer;
              }
            }
            .d2-container-card-bs__footer {
              border-top: $theme-container-border-outer;
              border-left: $theme-container-border-outer;
              border-right: $theme-container-border-outer;
              background: $theme-container-header-footer-background-color;
            }
          }
        }
      }
    }
  }
}
