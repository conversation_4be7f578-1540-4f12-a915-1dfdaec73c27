export default {
  ticket: {
    serverGroup: {
      visible: false
    },
    confluence: {
      visible: false
    },
    vpn: {
      visible: false
    },
    grafana: {
      visible: false
    },
    apollo: {
      permission: {
        visible: false
      },
      release: {
        visible: false
      }
    },
    nexus: {
      visible: false
    },
    ramPolicy: {
      visible: false
    },
    iamPolicy: {
      visible: false
    },
    awsIamUpdateLoginProfile: {
      visible: false
    },
    aliyunRamUpdateLoginProfile: {
      visible: false
    },
    nacos: {
      visible: false
    },
    gitlab: {
      project: { visible: false },
      group: { visible: false }
    },
    ons: {
      topic: { visible: false },
      group: { visible: false }
    },
    employeeResign: {
      visible: false
    },
    sqs: {
      visible: false
    },
    sns: {
      topic: { visible: false },
      subscription: { visible: false }
    },
    applicationScaleReplicas: {
      visible: false
    },
    applicationReduceReplicas: {
      visible: false
    },
    applicationDeploy: {
      visible: false
    },
    application: {
      permission: {
        visible: false
      },
      scaleReplicas: {
        visible: false
      },
      reduceReplicas: {
        visible: false
      },
      deploy: {
        visible: false
      },
      new: {
        visible: false
      },
      serDeploy: {
        visible: false
      }
    }
  }
}
