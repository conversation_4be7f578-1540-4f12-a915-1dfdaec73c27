name: Deploy preview

on:
  release:
    types: [published]

jobs:

  cdn:
    name: CDN
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v1
    - uses: bahmutov/npm-install@v1
    - name: Set vue cli env
      shell: bash
      run: |
        echo -e "\
        VUE_APP_PUBLIC_PATH=/d2-admin/preview/\
        " > .env.preview.local
        cat .env.preview.local | while read line
        do
          echo $line
        done
    - name: Build
      run: yarn build:preview --report
    - name: CDN download qshell
      run: |
        wget http://devtools.qiniu.com/qshell-linux-x86-v2.4.0.zip
        unzip qshell-linux-x86-v2.4.0.zip
        mv qshell-linux-x86-v2.4.0 qshell
    - name: CDN login
      run: ./qshell account ${{ secrets.AK }} ${{ secrets.SK }} GITHUB_ACTION
    - name: CDN upload
      run: |
        ./qshell qupload2 \
        --src-dir=$GITHUB_WORKSPACE/dist \
        --bucket=d2-cdn \
        --key-prefix=${GITHUB_REPOSITORY//*\//}/preview/ \
        --overwrite=true \
        --check-exists=true \
        --check-hash=true \
        --check-size=true \
        --rescan-local=true \
        --thread-count=32
    - name: CDN refresh
      run: |
        echo "https://cdn.d2.pub/${GITHUB_REPOSITORY//*\//}/preview/" > cdnrefresh.txt
        ./qshell cdnrefresh --dirs -i ./cdnrefresh.txt
  
  ftp:
    name: FTP
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v1
    - uses: bahmutov/npm-install@v1
    - name: Set vue cli env
      shell: bash
      run: |
        echo -e "\
        VUE_APP_PUBLIC_PATH=/d2-admin/preview/\
        " > .env.preview.local
        cat .env.preview.local | while read line
        do
          echo $line
        done
    - name: Build
      run: yarn build:preview --report
    - name: Deploy
      uses: SamKirkland/FTP-Deploy-Action@2.0.0
      env:
        FTP_SERVER: ${{ secrets.FTP_SERVER }}
        FTP_USERNAME: ${{ secrets.FTP_USERNAME }}
        FTP_PASSWORD: ${{ secrets.FTP_PASSWORD }}
        METHOD: sftp
        PORT: ${{ secrets.FTP_PORT }}
        LOCAL_DIR: dist
        REMOTE_DIR: /www/d2-admin/preview
        ARGS: --delete --verbose --parallel=100
  
  gh-pages:
    name: Github Pages
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v1
    - uses: bahmutov/npm-install@v1
    - name: Set vue cli env
      shell: bash
      run: |
        echo -e "\
        VUE_APP_PUBLIC_PATH=/d2-admin/\
        " > .env.preview.local
        cat .env.preview.local | while read line
        do
          echo $line
        done
    - name: Build
      run: yarn build:preview --report
    - name: Deploy
      uses: peaceiris/actions-gh-pages@v2
      env:
        PERSONAL_TOKEN: ${{ secrets.ACCESS_TOKEN }}
        PUBLISH_BRANCH: gh-pages
        PUBLISH_DIR: ./dist
      with:
        forceOrphan: true