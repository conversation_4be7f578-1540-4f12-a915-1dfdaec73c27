const DsInstanceType = Object.freeze({
  LDAP: {
    type: 1,
    name: 'LDA<PERSON>',
    instanceType: [
      {
        value: 'LDA<PERSON>',
        label: 'LDAP'
      }
    ]
  },
  JENKINS: {
    type: 2,
    name: '<PERSON><PERSON><PERSON>IN<PERSON>',
    instanceType: [
      {
        value: 'JEN<PERSON>IN<PERSON>',
        label: 'JENKINS'
      }
    ]
  },
  GITLAB: {
    type: 3,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    instanceType: [
      {
        value: 'GITLA<PERSON>',
        label: 'GITLA<PERSON>'
      }
    ]
  },
  SONAR: {
    type: 4,
    name: 'SON<PERSON>',
    instanceType: [
      {
        value: 'SON<PERSON>',
        label: 'SONAR'
      }
    ]
  },
  ANSIBLE: {
    type: 5,
    name: 'ANSIBL<PERSON>',
    instanceType: [
      {
        value: 'ANSIBLE',
        label: 'ANSIBLE'
      }
    ]
  },
  KUBERNETES: {
    type: 6,
    name: 'KUBERNETES',
    instanceType: [
      {
        value: 'KUBERNETES',
        label: 'KUBERNETES'
      }
    ]
  },
  ZABBIX: {
    type: 7,
    name: '<PERSON><PERSON><PERSON><PERSON>',
    instanceType: [
      {
        value: 'ZABBIX',
        label: 'ZABBIX'
      }
    ]
  },
  PROMETHEUS: {
    type: 8,
    name: 'PROMETHEUS',
    instanceType: []
  },
  GU<PERSON>AM<PERSON>E: {
    type: 9,
    name: 'GUACAMOLE',
    instanceType: [
      {
        value: 'GUACAMOLE',
        label: 'GUACAMOLE'
      }
    ]
  },
  NEXUS: {
    type: 10,
    name: 'NEXUS',
    instanceType: [
      {
        value: 'NEXUS',
        label: 'NEXUS'
      }
    ]
  },
  TENCENT_EXMAIL: {
    type: 11,
    name: 'TENCENT_EXMAIL',
    instanceType: [
      {
        value: 'TENCENT_EXMAIL',
        label: 'TENCENT_EXMAIL'
      }
    ]
  },
  NACOS: {
    type: 12,
    name: 'NACOS',
    instanceType: [
      {
        value: 'NACOS',
        label: 'NACOS'
      }
    ]
  },
  DINGTALK_ROBOT: {
    type: 13,
    name: 'DINGTALK_ROBOT',
    instanceType: [
      {
        value: 'DINGTALK_ROBOT',
        label: 'DINGTALK_ROBOT'
      }
    ]
  },
  DINGTALK_APP: {
    type: 14,
    name: 'DINGTALK_APP',
    instanceType: [
      {
        value: 'DINGTALK_APP',
        label: 'DINGTALK_APP'
      }
    ]
  },
  CONSUL: {
    type: 15,
    name: 'CONSUL',
    instanceType: [
      {
        value: 'CONSUL',
        label: 'CONSUL'
      }
    ]
  },
  ALIYUN: {
    type: 50,
    name: 'ALIYUN',
    instanceType: [
      {
        value: 'ALIYUN',
        label: 'ALIYUN'
      },
      {
        value: 'ECS',
        label: 'ECS'
      },
      {
        value: 'OSS',
        label: 'OSS'
      },
      {
        value: 'SLS',
        label: 'SLS'
      }
    ]
  },
  AWS: {
    type: 51,
    name: 'AWS',
    instanceType: [
      {
        value: 'EC2',
        label: 'EC2'
      }
    ]
  },
  HUAWEICLOUD: {
    type: 52,
    name: 'HUAWEICLOUD',
    instanceType: [
      {
        value: 'HUAWEICLOUD_ECS',
        label: 'HUAWEICLOUD_ECS'
      }
    ]
  },
  // 领先互联
  LXHL: {
    type: 53,
    name: 'LXHL',
    instanceType: [
      {
        value: 'LXHL',
        label: 'LXHL'
      }
    ]
  },
  ALIYUN_DEVOPS: {
    type: 54,
    name: 'ALIYUN_DEVOPS',
    instanceType: [
      {
        value: 'ALIYUN_DEVOPS',
        label: 'ALIYUN_DEVOPS'
      }
    ]
  },
  METER_SPHERE: {
    type: 55,
    name: 'METER_SPHERE',
    instanceType: [
      {
        value: 'METER_SPHERE',
        label: 'METER_SPHERE'
      }
    ]
  },
  APOLLO: {
    type: 56,
    name: 'APOLLO',
    instanceType: [
      {
        value: 'APOLLO',
        label: 'APOLLO'
      }
    ]
  },
  ALIYUN_ARMS: {
    type: 58,
    name: 'ALIYUN_ARMS',
    instanceType: [
      {
        value: 'ALIYUN_ARMS',
        label: 'ALIYUN_ARMS'
      }
    ]
  }
})

export default DsInstanceType
