<babeledit_project version="1.2">
    <!--

    BabelEdit project file
    https://www.codeandweb.com/babeledit

    This file contains meta data for all translations, but not the translation texts itself.
    They are stored in framework-specific message files (.json / .vue / .yaml / .properties)

    -->
    <preset_collections/>
    <framework>vue-json</framework>
    <filename>d2-admin.babel</filename>
    <source_root_dir></source_root_dir>
    <folder_node>
        <name></name>
        <children>
            <concept_node>
                <name>_element</name>
                <definition_loaded>false</definition_loaded>
                <description></description>
                <comment></comment>
                <default_text></default_text>
                <translations>
                    <translation>
                        <language>en-US</language>
                        <approved>false</approved>
                    </translation>
                    <translation>
                        <language>ja-JP</language>
                        <approved>false</approved>
                    </translation>
                    <translation>
                        <language>zh-CHS</language>
                        <approved>false</approved>
                    </translation>
                    <translation>
                        <language>zh-CHT</language>
                        <approved>false</approved>
                    </translation>
                </translations>
            </concept_node>
            <concept_node>
                <name>_name</name>
                <definition_loaded>false</definition_loaded>
                <description></description>
                <comment></comment>
                <default_text></default_text>
                <translations>
                    <translation>
                        <language>en-US</language>
                        <approved>false</approved>
                    </translation>
                    <translation>
                        <language>ja-JP</language>
                        <approved>false</approved>
                    </translation>
                    <translation>
                        <language>zh-CHS</language>
                        <approved>false</approved>
                    </translation>
                    <translation>
                        <language>zh-CHT</language>
                        <approved>false</approved>
                    </translation>
                </translations>
            </concept_node>
            <folder_node>
                <name>page</name>
                <children>
                    <folder_node>
                        <name>demo</name>
                        <children>
                            <folder_node>
                                <name>playground</name>
                                <children>
                                    <folder_node>
                                        <name>locales</name>
                                        <children>
                                            <concept_node>
                                                <name>text</name>
                                                <definition_loaded>false</definition_loaded>
                                                <description></description>
                                                <comment></comment>
                                                <default_text></default_text>
                                                <translations>
                                                    <translation>
                                                        <language>en-US</language>
                                                        <approved>false</approved>
                                                    </translation>
                                                    <translation>
                                                        <language>ja-JP</language>
                                                        <approved>false</approved>
                                                    </translation>
                                                    <translation>
                                                        <language>zh-CHS</language>
                                                        <approved>false</approved>
                                                    </translation>
                                                    <translation>
                                                        <language>zh-CHT</language>
                                                        <approved>false</approved>
                                                    </translation>
                                                </translations>
                                            </concept_node>
                                        </children>
                                    </folder_node>
                                </children>
                            </folder_node>
                        </children>
                    </folder_node>
                </children>
            </folder_node>
        </children>
    </folder_node>
    <isTemplateProject>false</isTemplateProject>
    <languages>
        <language>
            <code>en-US</code>
            <source_id></source_id>
            <source_file>src/locales/en.json</source_file>
        </language>
        <language>
            <code>ja-JP</code>
            <source_id></source_id>
            <source_file>src/locales/ja.json</source_file>
        </language>
        <language>
            <code>zh-CHS</code>
            <source_id></source_id>
            <source_file>src/locales/zh-chs.json</source_file>
        </language>
        <language>
            <code>zh-CHT</code>
            <source_id></source_id>
            <source_file>src/locales/zh-cht.json</source_file>
        </language>
    </languages>
    <translation_files>
        <translation_file>
            <file>src/locales/en.json</file>
        </translation_file>
        <translation_file>
            <file>src/locales/ja.json</file>
        </translation_file>
        <translation_file>
            <file>src/locales/zh-chs.json</file>
        </translation_file>
        <translation_file>
            <file>src/locales/zh-cht.json</file>
        </translation_file>
    </translation_files>
    <editor_configuration>
        <save_empty_translations>true</save_empty_translations>
        <copy_templates>
            <copy_template>$t('%1')</copy_template>
            <copy_template>{{ $t('%1') }}</copy_template>
            <copy_template>this.$t('%1')</copy_template>
        </copy_templates>
    </editor_configuration>
    <primary_language>zh-CHS</primary_language>
    <configuration>
        <indent>tab</indent>
        <format>namespaced-json</format>
    </configuration>
</babeledit_project>
